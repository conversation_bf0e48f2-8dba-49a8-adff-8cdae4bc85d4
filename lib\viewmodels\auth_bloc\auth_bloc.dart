import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/models/auth_model/login_model.dart';
import 'package:room_eight/models/auth_model/profile_detail_model.dart';
import 'package:room_eight/models/auth_model/signup_model.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  static const int initialTime = 60;
  Timer? _timer;
  final AuthRepository authRepository;

  AuthBloc(this.authRepository)
    : super(
        AuthState(
          loginFormKey: GlobalKey<FormState>(),
          signupFormKey: GlobalKey<FormState>(),
          addPersonalDetailFormKey: GlobalKey<FormState>(),
          phoneController: TextEditingController(),
          phonefocusnode: FocusNode(),
          emailController: TextEditingController(),
          passwordController: TextEditingController(),
          emailFocusNode: FocusNode(),
          passwordFocusNode: FocusNode(),
          obscurePassword: true,
          signupNameController: TextEditingController(),
          signupNamefocusnode: FocusNode(),
          signupEmailController: TextEditingController(),
          signupEmailfocusnode: FocusNode(),
          signupPasswordController: TextEditingController(),
          signupPasswordfocusnode: FocusNode(),
          signupConfirmPasswordController: TextEditingController(),
          signupConfirmPasswordfocusnode: FocusNode(),
        ),
      ) {
    on<EmailChanged>(_onEmailChanged);
    on<PasswordChanged>(_onPasswordChanged);
    on<TogglePasswordVisibility>(_onTogglePasswordVisibility);
    on<ToggleAgreementAccepted>(_onToggleAgreementAccepted);
    on<ToggleSignupPasswordVisibility>(_onToggleSignupPasswordVisibility);
    on<ToggleSignupConfirmPasswordVisibility>(
      _onToggleSignupConfirmPasswordVisibility,
    );
    on<SignupNameChanged>(_onSignupNameChanged);
    on<SignupEmailChanged>(_onSignupEmailChanged);
    on<SignupPasswordChanged>(_onSignupPasswordChanged);
    on<SignupConfirmPasswordChanged>(_onSignupConfirmPasswordChanged);
    on<LoginSubmitted>(_onLoginSubmitted);
    on<SignupSubmitted>(_onSignupSubmitted);
    on<CreateUserProfileSubmitted>(_onCreateUserProfileSubmitted);
  }

  void _onEmailChanged(EmailChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(emailController: state.emailController));
  }

  void _onPasswordChanged(PasswordChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(passwordController: state.passwordController));
  }

  void _onTogglePasswordVisibility(
    TogglePasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void _onToggleSignupPasswordVisibility(
    ToggleSignupPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(signupObscurePassword: !state.signupObscurePassword));
  }

  void _onToggleSignupConfirmPasswordVisibility(
    ToggleSignupConfirmPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        signupObscureConfirmPassword: !state.signupObscureConfirmPassword,
      ),
    );
  }

  FutureOr<void> _onToggleAgreementAccepted(
    ToggleAgreementAccepted event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(isAgreementAccepted: !state.isAgreementAccepted));
  }

  void _onSignupNameChanged(SignupNameChanged event, Emitter<AuthState> emit) {
    emit(state.copyWith(signupNameController: state.signupNameController));
  }

  void _onSignupEmailChanged(
    SignupEmailChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(signupEmailController: state.signupEmailController));
  }

  void _onSignupPasswordChanged(
    SignupPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(signupPasswordController: state.signupPasswordController),
    );
  }

  void _onSignupConfirmPasswordChanged(
    SignupConfirmPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        signupConfirmPasswordController: state.signupConfirmPasswordController,
      ),
    );
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));

      LoginResponse loginResponse = await authRepository.loginCall(
        loginEmail: state.emailController?.text ?? "",
        loginPassword: state.passwordController?.text ?? "",
      );

      Prefobj.preferences?.put(Prefkeys.IS_LOGIN, true);
      Prefobj.preferences?.put(Prefkeys.AUTHTOKEN, loginResponse.token);
      Prefobj.preferences?.put(
        Prefkeys.USER_MAIL_ID,
        loginResponse.data?.email,
      );

      Logger.lOG("Login successful: $loginResponse");
      NavigatorService.pushNamed(AppRoutes.roomEightNavBar);
      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG(e.toString());
    } finally {
      emit(state.copyWith(isloginLoading: false));
    }
  }

  Future<void> _onSignupSubmitted(
    SignupSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isSignupLoading: true));

      SignUpResponse signUpResponse = await authRepository.signUpCall(
        name: state.signupNameController?.text ?? "",
        signUpEmail: state.signupEmailController?.text ?? "",
        signUpPassword: state.signupPasswordController?.text ?? "",
      );

      Logger.lOG("Signup successful: ${signUpResponse.toString()}");

      NavigatorService.pushNamed(AppRoutes.welcomeScreen);

      emit(state.copyWith(isSignupLoading: false));
    } catch (e) {
      Logger.lOG("Signup error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isSignupLoading: false));
    }
  }

  Future<void> _onCreateUserProfileSubmitted(
    CreateUserProfileSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isCreateUserProfileLoading: true));

      UserProfile signUpResponse = await authRepository.profileDetailCall(
        data: {
          "full_name":
              state.signupNameController?.text ?? userProfile.fullName ?? "",
          "dob": userProfile.dob ?? "",
          "gender": userProfile.gender ?? "",
          "prefered_gender": userProfile.preferedGender ?? "",
          "prefered_smoking": userProfile.preferedSmoking ?? "",
          "cleaniness": userProfile.cleaniness ?? "",
          "is_having_pet": userProfile.isHavingPet ?? "",
          "class_standing": userProfile.classStanding ?? "",
          "habits_lifestyle": userProfile.habitsLifestyle ?? "",
          "living_style": userProfile.livingStyle ?? "",
          "interests_hobbies": userProfile.interestsHobbies ?? "",
          "about": userProfile.about ?? "",
          "contact_number": userProfile.contactNumber ?? "",
          "prefered_lease_period": userProfile.preferedLeasePeriod ?? "",
          "prefered_locations": userProfile.preferedLocations ?? "",
          "personality_type_description":
              userProfile.personalityTypeDescription ?? "",
          "is_verified": userProfile.isVerified ?? false,
          "is_active": userProfile.isActive ?? true,
          "updated_at": userProfile.updatedAt ?? "",
          "profile_picture": userProfile.profilePicture ?? "",
          "profile_pictures": userProfile.profilePictures ?? [],
        },
      ); 

      Logger.lOG(
        "Create User Profile successful: ${signUpResponse.toString()}",
      );

      // NavigatorService.pushNamed(AppRoutes.welcomeScreen);

      emit(state.copyWith(isCreateUserProfileLoading: false));
    } catch (e) {
      Logger.lOG("Create User Profile error: ${e.toString()}");
    } finally {
      emit(state.copyWith(isCreateUserProfileLoading: false));
    }
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
