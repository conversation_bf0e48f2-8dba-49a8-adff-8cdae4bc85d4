part of 'profile_bloc.dart';

abstract class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object?> get props => [];
}

class PhoneChanged extends ProfileEvent {
  final String phone;
  const PhoneChanged(this.phone);

  @override
  List<Object?> get props => [phone];
}

class LeasePeriodChanged extends ProfileEvent {
  final String leasePeriod;
  const LeasePeriodChanged(this.leasePeriod);

  @override
  List<Object?> get props => [leasePeriod];
}

class AddPhoto extends ProfileEvent {
  final String photoPath;
  const AddPhoto(this.photoPath);

  @override
  List<Object?> get props => [photoPath];
}

class RemovePhoto extends ProfileEvent {
  final String photoPath;
  const RemovePhoto(this.photoPath);

  @override
  List<Object?> get props => [photoPath];
}

class ProfileImageChanged extends ProfileEvent {
  final String imagePath;
  const ProfileImageChanged(this.imagePath);

  @override
  List<Object?> get props => [imagePath];
}

class NameChanged extends ProfileEvent {
  final String name;
  const NameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class EmailChanged extends ProfileEvent {
  final String email;
  const EmailChanged(this.email);

  @override
  List<Object?> get props => [email];
}

class GenderChanged extends ProfileEvent {
  final String gender;
  const GenderChanged(this.gender);

  @override
  List<Object?> get props => [gender];
}

class AgeChanged extends ProfileEvent {
  final String age;
  const AgeChanged(this.age);

  @override
  List<Object?> get props => [age];
}

class AddPersonalityTag extends ProfileEvent {
  final String tag;
  const AddPersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class RemovePersonalityTag extends ProfileEvent {
  final String tag;
  const RemovePersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class AddCustomPersonalityTag extends ProfileEvent {
  final String tag;
  const AddCustomPersonalityTag(this.tag);

  @override
  List<Object?> get props => [tag];
}

class SaveProfile extends ProfileEvent {
  const SaveProfile();
}

class FullProfileNameChanged extends ProfileEvent {
  final String fullName;
  const FullProfileNameChanged(this.fullName);
}

class ProfileDateOfBirthChanged extends ProfileEvent {
  final String dateOfBirth;
  const ProfileDateOfBirthChanged(this.dateOfBirth);
}

class ProfileSubmitted extends ProfileEvent {}

class FinalProfileSubmitted extends ProfileEvent {}

class SelectUserProfile extends ProfileEvent {}

class SelectPeriod extends ProfileEvent {
  final String period;

  const SelectPeriod({required this.period});
}

class SelectGender extends ProfileEvent {
  final String gender;

  const SelectGender({required this.gender});
}

class SelectPreferredGender extends ProfileEvent {
  final String preferredGender;

  const SelectPreferredGender({required this.preferredGender});
}

class SelectSmokingPerson extends ProfileEvent {
  final String type;

  const SelectSmokingPerson({required this.type});
}

class SelectCleanLevel extends ProfileEvent {
  final String level;

  const SelectCleanLevel({required this.level});
}

class SelectPet extends ProfileEvent {
  final String pet;

  const SelectPet({required this.pet});
}

class SelectClassStand extends ProfileEvent {
  final String classStand;

  const SelectClassStand({required this.classStand});
}

class SelectHabitsAndLifestyle extends ProfileEvent {
  final String habitsAndLifestyle;

  const SelectHabitsAndLifestyle({required this.habitsAndLifestyle});
}

class SelectCleanlinessLivingStyle extends ProfileEvent {
  final String cleanlinessLivingStyle;

  const SelectCleanlinessLivingStyle({required this.cleanlinessLivingStyle});
}

class SelectInterestsHobbies extends ProfileEvent {
  final String interestsHobbies;

  const SelectInterestsHobbies({required this.interestsHobbies});
}

class TogglePickThings extends ProfileEvent {}

class SearchChanged extends ProfileEvent {
  final String searchText;
  const SearchChanged(this.searchText);
}

class SelectMultiplePhotos extends ProfileEvent {
  final List<String> photoPaths;
  const SelectMultiplePhotos(this.photoPaths);

  @override
  List<Object?> get props => [photoPaths];
}

class CreateUserProfileSubmitted extends ProfileEvent {}
